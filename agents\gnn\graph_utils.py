import numpy as np
import torch
from torch_geometric.data import Data
import networkx as nx
import matplotlib.pyplot as plt
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from config import DEVICE

class IEEE30GraphBuilder:
    """
    IEEE30电力系统图构建工具
    - 将IEEE30系统的原始数据转换为GNN可处理的图数据结构
    - 支持可视化电力系统图
    """
    def __init__(self, num_buses=30, num_branches=41, num_gens=6, branch_data=None):
        """
        初始化图构建器
        Args:
            num_buses: 母线数量
            num_branches: 支路数量
            num_gens: 发电机数量
            branch_data: 支路数据，用于动态构建拓扑（可选）
        """
        self.num_buses = num_buses
        self.num_branches = num_branches
        self.num_gens = num_gens
        self.branch_data = branch_data

        # 发电机与母线的连接关系(从1开始索引)
        # IEEE30 case的生成器连接在1,2,5,8,11,13号母线上
        # 这里索引从0开始，所以减1
        self.gen_bus_map = [0, 1, 4, 7, 10, 12]

        # 记录每个母线是否连接发电机及其编号
        self.bus_gen_map = {bus_idx: -1 for bus_idx in range(num_buses)}
        for gen_idx, bus_idx in enumerate(self.gen_bus_map):
            self.bus_gen_map[bus_idx] = gen_idx

        # 创建静态边索引 - 根据IEEE30系统的拓扑结构
        # 如果提供了branch_data，使用动态拓扑；否则使用默认拓扑
        self._create_static_edges()

        self.graph_cache = {}  # 添加缓存字典
        self.cache_size_limit = 1000  # 设置缓存大小限制
    
    def _create_static_edges(self):
        """创建电力系统的静态拓扑图 - 从PYPOWER的case30数据中提取或使用动态数据"""

        if self.branch_data is not None:
            # 使用动态branch数据构建拓扑
            branch_connections = []
            for i in range(len(self.branch_data)):
                # 从branch数据中提取起始和终止节点（转换为0-based索引）
                f_bus = int(self.branch_data[i, 0]) - 1  # 起始节点
                t_bus = int(self.branch_data[i, 1]) - 1  # 终止节点
                branch_connections.append([f_bus, t_bus])

            print(f"使用动态拓扑构建图，包含{len(branch_connections)}条线路", flush=True)
        else:
            # 使用默认的IEEE30拓扑（41条线路）
            # 这里的边是从case30中的branch数据提取的 (从1开始索引，这里转为从0开始)
            branch_connections = [
                [0, 1], [0, 2], [1, 3], [2, 3], [1, 4], [2, 4], [3, 5], [4, 5],
                [5, 6], [5, 7], [7, 8], [7, 9], [8, 9], [8, 10], [9, 10], [9, 11],
                [10, 19], [11, 12], [11, 13], [12, 13], [12, 14], [13, 14], [13, 15],
                [14, 15], [14, 16], [15, 16], [15, 17], [16, 17], [16, 18], [17, 18],
                [17, 19], [19, 20], [20, 21], [20, 22], [21, 22], [21, 23], [22, 23],
                [22, 24], [23, 24], [23, 25], [24, 25], [24, 26], [25, 26], [25, 27],
                [26, 27], [26, 28], [26, 29], [27, 29], [28, 29]
            ]
            print(f"使用默认IEEE30拓扑构建图，包含{len(branch_connections)}条线路", flush=True)

        # 转换为双向边 - 源节点到目标节点，目标节点到源节点
        edge_index = []
        for source, target in branch_connections:
            edge_index.append([source, target])
            edge_index.append([target, source])  # 添加反向边

        # 转换为PyTorch Geometric需要的格式 - COO格式的稀疏邻接矩阵
        edge_index = np.array(edge_index).T
        self.static_edge_index = torch.tensor(edge_index, dtype=torch.long).to(DEVICE)
    
    def _create_node_features(self, load_data, gen_data, wind_data=None, wind_bus_idx=None):
        """
        创建节点特征 - 兼容GPU张量，支持风电节点处理
        Args:
            load_data: 每个母线的负荷数据 [num_buses]
            gen_data: 每个发电机的出力数据 [num_gens]
            wind_data: 风电可用容量数据 (标量或张量)
            wind_bus_idx: 风电连接的母线索引
        Returns:
            node_features: 节点特征张量 [num_buses, 3]
                特征0: 节点负荷需求（归一化）
                特征1: 节点类型 (0.0=纯负荷, 1.0=常规发电机, 2.0=风电节点)
                特征2: 发电/风电信息 (发电机出力或风电可用容量)
        """
        # 检查输入是否为CUDA张量
        is_tensor = isinstance(load_data, torch.Tensor)
        is_cuda = is_tensor and load_data.device.type == 'cuda'
        
        # 创建节点特征矩阵 - 根据输入类型决定创建方式
        if is_tensor:
            # 在GPU上创建张量 - 保持3个特征，但增强风电处理
            node_features = torch.zeros((self.num_buses, 3), dtype=torch.float, device=load_data.device)

            # 设置负荷特征
            node_features[:, 0] = load_data

            # 设置发电机连接和出力特征
            for bus_idx, gen_idx in self.bus_gen_map.items():
                if gen_idx >= 0:  # 母线连接有发电机
                    node_features[bus_idx, 1] = 1.0  # 标记有发电机连接
                    node_features[bus_idx, 2] = gen_data[gen_idx]  # 发电机出力

            # 处理风电节点特征（如果提供了风电数据）
            if wind_data is not None and wind_bus_idx is not None:
                # 风电节点标记为特殊类型的"发电机"
                node_features[wind_bus_idx, 1] = 2.0  # 2.0表示风电节点（区别于常规发电机的1.0）
                # 将风电可用容量作为"出力"特征
                if isinstance(wind_data, torch.Tensor):
                    node_features[wind_bus_idx, 2] = wind_data.item() if wind_data.numel() == 1 else wind_data[0]
                else:
                    node_features[wind_bus_idx, 2] = float(wind_data)

            return node_features
        else:
            # 如果输入是CUDA张量，先将其移动到CPU
            if is_cuda:
                load_data = load_data.cpu().numpy()
                if gen_data is not None and isinstance(gen_data, torch.Tensor) and gen_data.device.type == 'cuda':
                    gen_data = gen_data.cpu().numpy()
            
            # 原始NumPy实现 - 保持3个特征，但增强风电处理
            node_features = np.zeros((self.num_buses, 3))

            # 设置负荷特征
            node_features[:, 0] = load_data

            # 设置发电机连接和出力特征
            for bus_idx, gen_idx in self.bus_gen_map.items():
                if gen_idx >= 0:  # 母线连接有发电机
                    node_features[bus_idx, 1] = 1  # 标记有发电机连接
                    node_features[bus_idx, 2] = gen_data[gen_idx]  # 发电机出力

            # 处理风电节点特征（如果提供了风电数据）
            if wind_data is not None and wind_bus_idx is not None:
                # 风电节点标记为特殊类型的"发电机"
                node_features[wind_bus_idx, 1] = 2.0  # 2.0表示风电节点（区别于常规发电机的1.0）
                # 将风电可用容量作为"出力"特征
                if isinstance(wind_data, torch.Tensor):
                    node_features[wind_bus_idx, 2] = wind_data.item() if wind_data.numel() == 1 else wind_data[0]
                elif hasattr(wind_data, '__len__') and len(wind_data) > 0:
                    node_features[wind_bus_idx, 2] = float(wind_data[0])
                else:
                    node_features[wind_bus_idx, 2] = float(wind_data)

            return torch.tensor(node_features, dtype=torch.float).to(DEVICE)
    
    def _create_edge_features(self, branch_flows=None):
        """
        创建边特征 - 兼容GPU张量
        Args:
            branch_flows: 支路潮流数据 [num_branches] (可选)
        Returns:
            edge_features: 边特征张量 [num_edges, num_features]
        """
        # 边的数量 (考虑双向边)
        num_edges = self.static_edge_index.shape[1]
        
        # 检查是否在GPU上
        is_gpu_tensor = branch_flows is not None and isinstance(branch_flows, torch.Tensor) and branch_flows.is_cuda
        
        if branch_flows is not None:
            if is_gpu_tensor:
                # 直接在GPU上创建边特征
                edge_features = torch.zeros((num_edges, 1), dtype=torch.float, device=branch_flows.device)
                
                # 设置支路潮流
                for i in range(self.num_branches):
                    # 使用abs函数处理张量
                    flow_value = torch.abs(branch_flows[i])
                    edge_features[i*2, 0] = flow_value
                    edge_features[i*2+1, 0] = flow_value
                    
                return edge_features
            else:
                # 如果是CUDA张量但未被识别为is_gpu_tensor（可能因为.is_cuda检查不完整），先移到CPU
                if branch_flows is not None and isinstance(branch_flows, torch.Tensor) and branch_flows.device.type == 'cuda':
                    branch_flows = branch_flows.cpu().numpy()
                    
                # NumPy版本
                edge_features = np.zeros((num_edges, 1))
                
                # 设置支路潮流
                for i in range(self.num_branches):
                    edge_features[i*2, 0] = abs(branch_flows[i])
                    edge_features[i*2+1, 0] = abs(branch_flows[i])
                
                return torch.tensor(edge_features, dtype=torch.float).to(DEVICE)
        else:
            # 如果没有提供支路潮流，就用全1特征
            return torch.ones((num_edges, 1), dtype=torch.float).to(DEVICE)
    
    def build_graph(self, load_data, gen_data, wind_data=None, wind_bus_idx=None, branch_flows=None):
        """
        构建电力系统图
        Args:
            load_data: 每个母线的负荷数据 [num_buses]
            gen_data: 每个发电机的出力数据 [num_gens]
            wind_data: 风电出力数据 (已移除)
            wind_bus_idx: 风电连接的母线索引 (已移除)
            branch_flows: 支路潮流数据 [num_branches] (可选)
        Returns:
            graph: PyTorch Geometric的Data对象
        """
        # 1. 创建节点特征
        node_features = self._create_node_features(load_data, gen_data, wind_data, wind_bus_idx)
        
        # 2. 创建边特征
        edge_features = self._create_edge_features(branch_flows)
        
        # 3. 构建图数据
        graph = Data(
            x=node_features,
            edge_index=self.static_edge_index,
            edge_attr=edge_features
        )
        
        # 重要：设置batch属性，用于global_mean_pool
        # 这里batch属性应该设置为全0向量，因为只有一个图
        batch = torch.zeros(node_features.size(0), dtype=torch.long).to(DEVICE)
        graph.batch = batch
        
        return graph
    
    def build_time_series_graphs(self, load_series, gen_series, 
                              wind_series=None, wind_bus_idx=None, 
                              branch_flow_series=None, time_steps=16, use_cache=True):
        """
        构建时间序列图数据 - 带缓存机制
        注意：风电相关参数已移除，仅保留参数兼容性
        """
        # 如果启用缓存，尝试查找缓存
        if use_cache and len(self.graph_cache) < self.cache_size_limit:
            if isinstance(load_series, torch.Tensor):
                load_hash = load_series.sum().item()
            else:
                load_hash = float(np.sum(load_series))
                
            if isinstance(gen_series, torch.Tensor):
                gen_hash = gen_series.sum().item()
            else:
                gen_hash = float(np.sum(gen_series))
                
            wind_hash = 0
            if wind_series is not None:
                if isinstance(wind_series, torch.Tensor):
                    wind_hash = wind_series.sum().item()
                else:
                    wind_hash = float(np.sum(wind_series))
            
            cache_key = (load_hash, gen_hash, wind_hash, wind_bus_idx)
            
            if cache_key in self.graph_cache:
                return self.graph_cache[cache_key]
        
        # 检查输入类型并转换为numpy数组（如果是PyTorch张量）
        if isinstance(load_series, torch.Tensor):
            # 确保张量在CPU上
            if load_series.device.type == 'cuda':
                load_series = load_series.cpu().numpy()
            else:
                load_series = load_series.numpy()
        
        if isinstance(gen_series, torch.Tensor):
            # 确保张量在CPU上
            if gen_series.device.type == 'cuda':
                gen_series = gen_series.cpu().numpy()
            else:
                gen_series = gen_series.numpy()
        
        if wind_series is not None and isinstance(wind_series, torch.Tensor):
            # 确保张量在CPU上
            if wind_series.device.type == 'cuda':
                wind_series = wind_series.cpu().numpy()
            else:
                wind_series = wind_series.numpy()
        
        if branch_flow_series is not None and isinstance(branch_flow_series, torch.Tensor):
            # 确保张量在CPU上
            if branch_flow_series.device.type == 'cuda':
                branch_flow_series = branch_flow_series.cpu().numpy()
            else:
                branch_flow_series = branch_flow_series.numpy()
            
        graph_list = []
        
        for t in range(time_steps):
            # 提取当前时间步的数据
            current_load = load_series[t]
            current_gen = gen_series[t]
            
            # 风电功能已禁用
            current_wind = None
                
            current_flows = None
            if branch_flow_series is not None:
                current_flows = branch_flow_series[t]
            
            # 构建当前时间步的图
            graph = self.build_graph(
                current_load, current_gen, 
                wind_data=current_wind, wind_bus_idx=wind_bus_idx,
                branch_flows=current_flows
            )
            
            # 重要：确保每个图都有独立的batch属性
            # 对于时间序列中每个图的每个节点，batch属性设置为t
            graph.batch = torch.full((graph.x.size(0),), t, dtype=torch.long, device=DEVICE)
            
            graph_list.append(graph)
            
        # 保存到缓存
        if use_cache and len(self.graph_cache) < self.cache_size_limit:
            self.graph_cache[cache_key] = graph_list
            
            # 如果缓存过大，移除最早的项
            if len(self.graph_cache) > self.cache_size_limit:
                self.graph_cache.pop(next(iter(self.graph_cache)))
                
        return graph_list
    
    def visualize_power_system(self, node_values=None, edge_values=None, 
                            node_labels=None, show_gen=True, title='IEEE 30 Power System'):
        """
        可视化电力系统图
        Args:
            node_values: 节点值，用于着色 (可选)
            edge_values: 边值，用于着色 (可选)
            node_labels: 节点标签 (可选)
            show_gen: 是否显示发电机位置
            title: 图标题
        """
        # 创建无向图
        G = nx.Graph()
        
        # 添加节点
        for i in range(self.num_buses):
            G.add_node(i)
            
        # 添加边 (不重复)
        edge_set = set()
        for i in range(self.static_edge_index.shape[1]):
            source = self.static_edge_index[0, i].item()
            target = self.static_edge_index[1, i].item()
            
            if (source, target) not in edge_set and (target, source) not in edge_set:
                G.add_edge(source, target)
                edge_set.add((source, target))
        
        # 设置布局
        pos = nx.spring_layout(G, seed=42)
        
        plt.figure(figsize=(12, 10))
        
        # 绘制边
        if edge_values is not None:
            # 边着色
            edges = nx.draw_networkx_edges(
                G, pos, 
                width=2,
                edge_color=edge_values,
                edge_cmap=plt.cm.Blues
            )
        else:
            # 默认边
            edges = nx.draw_networkx_edges(G, pos, width=1.5)
        
        # 节点颜色映射
        node_colors = ['lightblue'] * self.num_buses
        
        # 标记发电机节点
        if show_gen:
            for bus_idx in self.gen_bus_map:
                node_colors[bus_idx] = 'orange'
        
        # 使用自定义节点值进行着色
        if node_values is not None:
            nodes = nx.draw_networkx_nodes(
                G, pos,
                node_size=500,
                node_color=node_values,
                cmap=plt.cm.Reds
            )
            plt.colorbar(nodes)
        else:
            # 使用预定义的颜色
            nodes = nx.draw_networkx_nodes(
                G, pos,
                node_size=500,
                node_color=node_colors
            )
        
        # 添加节点标签
        if node_labels is not None:
            nx.draw_networkx_labels(G, pos, labels=node_labels, font_size=10)
        else:
            # 默认标签是节点索引
            labels = {i: str(i+1) for i in range(self.num_buses)}  # 显示从1开始的编号
            nx.draw_networkx_labels(G, pos, labels=labels, font_size=10)
            
        # 添加图例
        if show_gen:
            import matplotlib.patches as mpatches
            gen_patch = mpatches.Patch(color='orange', label='Generator Bus')
            load_patch = mpatches.Patch(color='lightblue', label='Load Bus')
            plt.legend(handles=[gen_patch, load_patch])
        
        plt.title(title)
        plt.axis('off')
        plt.tight_layout()
        
        return plt.gcf()

    def build_time_series_graphs_optimized(self, load_series, gen_series, 
                                         wind_series=None, wind_bus_idx=None):
        """
        构建时间序列图数据 - 优化版本，直接使用批量GPU操作
        注意：风电相关参数已移除，仅保留参数兼容性
        """
        time_steps = load_series.shape[0]
        graph_list = []
        
        # 创建批次索引 - 一次性创建所有批次索引
        batch_indices = torch.arange(time_steps, device=DEVICE).repeat_interleave(self.num_buses)
        
        for t in range(time_steps):
            # 提取当前时间步的数据
            current_load = load_series[t]
            current_gen = gen_series[t]
            
            # 风电特征已移除
            current_wind = None
                
            # 构建当前时间步的图
            graph = self.build_graph(
                current_load, current_gen, 
                wind_data=current_wind, wind_bus_idx=wind_bus_idx
            )
            
            # 为每个图设置批次索引
            graph.batch = batch_indices[t * self.num_buses:(t + 1) * self.num_buses]
            
            graph_list.append(graph)
            
        return graph_list

def state_to_graph(state, graph_builder, time_steps=1, node_feature_dim=None):
    """
    将状态向量转换为图数据列表 - 优化版本，支持实时调度，已移除风电特征
    """
    # 处理实时调度的情况（time_steps=1）
    if time_steps == 1:
        # 检查输入类型并转换为张量
        if isinstance(state, np.ndarray):
            state_tensor = torch.tensor(state, dtype=torch.float32, device=DEVICE)
        elif isinstance(state, torch.Tensor):
            if state.device != DEVICE:
                state_tensor = state.to(DEVICE)
            else:
                state_tensor = state
        else:
            raise TypeError(f"状态向量类型不支持: {type(state)}")
        
        # 对于单时间步，直接解析状态向量
        loads = state_tensor[:30]  # 前30个元素是负荷
        gens = state_tensor[30:36]  # 接下来6个元素是发电机

        # 检查是否包含风电信息（状态向量长度为37）
        if len(state_tensor) == 37:
            wind = state_tensor[36:37]  # 第37个元素是风电可用容量
        else:
            wind = None
        
        # 构建单个图
        # 如果有风电信息，传递给图构建器；同时传递风电接入节点索引
        if wind is not None:
            graph = graph_builder.build_graph(
                loads, gens,
                wind_data=wind,
                wind_bus_idx=6  # 风电接入节点7（0-indexed为6）
            )
        else:
            graph = graph_builder.build_graph(
                loads, gens,
                wind_data=None,
                wind_bus_idx=None
            )
        
        # 返回包含单个图的列表
        return [graph]
    
    # 处理多时间步的情况（向后兼容）
    else:
        # 计算每个时间步的特征维度
        if node_feature_dim is None:
            step_dim = 36  # 30个母线负荷 + 6个发电机
        else:
            step_dim = node_feature_dim
        
        # 检查维度是否匹配
        if time_steps * step_dim != state.shape[0]:
            raise ValueError(f"状态向量大小 {state.shape[0]} 无法重塑为 ({time_steps}, {step_dim})")
        
        # 优化：直接将整个状态向量转换为张量并放到GPU
        if isinstance(state, np.ndarray):
            state_tensor = torch.tensor(state, dtype=torch.float32, device=DEVICE)
        elif isinstance(state, torch.Tensor):
            if state.device != DEVICE:
                state_tensor = state.to(DEVICE)
            else:
                state_tensor = state
        else:
            raise TypeError(f"状态向量类型不支持: {type(state)}")
        
        # 将扁平状态向量重组为时间步序列
        state_reshaped = state_tensor.reshape(time_steps, step_dim)
        
        # 优化：直接在GPU上处理数据分离
        # 矢量化操作：一次性提取所有时间步的数据
        loads_all = state_reshaped[:, :30]  # [time_steps, 30]
        gens_all = state_reshaped[:, 30:36]  # [time_steps, 6]
        wind_all = None
        
        # 调用优化后的图构建方法
        return graph_builder.build_time_series_graphs_optimized(
            loads_all, gens_all, 
            wind_series=None, 
            wind_bus_idx=None
        ) 